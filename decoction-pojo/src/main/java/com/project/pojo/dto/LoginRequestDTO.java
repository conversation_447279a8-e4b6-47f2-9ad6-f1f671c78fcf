package com.project.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 登录请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@ApiModel("登录请求")
public class LoginRequestDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 登录类型：STAFF-员工登录，PATIENT-患者登录
     */
    @ApiModelProperty(value = "登录类型", required = true, example = "STAFF")
    @NotBlank(message = "登录类型不能为空")
    private String loginType;
    
    /**
     * 用户名（员工登录使用）
     */
    @ApiModelProperty("用户名")
    private String username;
    
    /**
     * 密码（员工登录使用）
     */
    @ApiModelProperty("密码")
    private String password;
    
    /**
     * 手机号（患者登录使用）
     */
    @ApiModelProperty("手机号")
    private String phone;
    
    /**
     * 身份证号（患者登录使用）
     */
    @ApiModelProperty("身份证号")
    private String idCard;
}
