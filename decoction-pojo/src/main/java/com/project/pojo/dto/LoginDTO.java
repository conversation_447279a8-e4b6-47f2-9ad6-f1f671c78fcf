package com.project.pojo.dto;

import java.io.Serializable;
import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;

/**
 * 基础 DTO 类
 *
 * <AUTHOR>
 * @since 2025-06-30
 */

@Data
@ApiModel("登录接口DTO")
public class LoginDto implements Serializable {
@NotBlank(message = "用户名不能为空")
    @ApiModelProperty("用户名")
    private String username;

    @NotBlank(message = "密码不能为空")
    @ApiModelProperty("密码")
    private String password;
}
