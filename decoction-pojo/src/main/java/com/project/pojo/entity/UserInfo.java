package com.project.pojo.entity;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("user_info")
@ApiModel("用户信息")
public class UserInfo implements Serializable{
    private static final long serivalVersionUID  = 1L;
    /**
     * 用户ID
     */
    @TableId(value = "user_id", type = IdType.AUTO)
    @ApiModelProperty("用户ID")
    private Integer userId;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 性别（男/女）
     */
    @ApiModelProperty("性别")
    private String gender;

    /**
     * 年龄（18-100）
     */
    @ApiModelProperty("年龄")
    private Integer age;

    /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    private String phone;

    /**
     * 身份证号
     */
    @ApiModelProperty("身份证号")
    private String idCard;

    /**
     * 角色
     */
    @ApiModelProperty("角色")
    private String role;

    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String username;

    /**
     * 密码
     */
    @ApiModelProperty("密码")
    private String password;

    /**
     * 签名数据
     */
    @ApiModelProperty("签名数据")
    private String signData;

    /**
     * 状态（true-启用，false-禁用）
     */
    @ApiModelProperty("状态")
    private Boolean status;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedAt;

}