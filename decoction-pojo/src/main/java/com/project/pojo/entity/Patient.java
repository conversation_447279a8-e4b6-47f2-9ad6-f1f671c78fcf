package com.project.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 患者信息实体
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@TableName("patient")
@ApiModel("患者信息")
public class Patient implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 患者ID
     */
    @TableId(value = "patient_id", type = IdType.AUTO)
    @ApiModelProperty("患者ID")
    private Integer patientId;
    
    /**
     * 患者姓名
     */
    @ApiModelProperty("患者姓名")
    private String name;
    
    /**
     * 性别（男/女）
     */
    @ApiModelProperty("性别")
    private String gender;
    
    /**
     * 出生日期
     */
    @ApiModelProperty("出生日期")
    private LocalDate birthDate;
    
    /**
     * 年龄
     */
    @ApiModelProperty("年龄")
    private Integer age;
    
    /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    private String phone;
    
    /**
     * 身份证号
     */
    @ApiModelProperty("身份证号")
    private String idCard;
    
    /**
     * 地址
     */
    @ApiModelProperty("地址")
    private String address;
    
    /**
     * 紧急联系人
     */
    @ApiModelProperty("紧急联系人")
    private String emergencyContact;
    
    /**
     * 紧急联系人电话
     */
    @ApiModelProperty("紧急联系人电话")
    private String emergencyPhone;
    
    /**
     * 过敏史
     */
    @ApiModelProperty("过敏史")
    private String allergies;
    
    /**
     * 病史
     */
    @ApiModelProperty("病史")
    private String medicalHistory;
    
    /**
     * 状态（true-启用，false-禁用）
     */
    @ApiModelProperty("状态")
    private Boolean status;
    
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedAt;
}
