package com.project.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.project.pojo.entity.UserInfo;

/**
 * 用户信息Service接口
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
public interface UserInfoService extends IService<UserInfo> {

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    UserInfo getByUsername(String username);

    /**
     * 添加管理员用户
     *
     * @param userInfo 用户信息
     * @return 是否成功
     */
    boolean addAdmin(UserInfo userInfo);

    /**
     * 更新用户状态
     *
     * @param userId 用户ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateStatus(Integer userId, Boolean status);
}