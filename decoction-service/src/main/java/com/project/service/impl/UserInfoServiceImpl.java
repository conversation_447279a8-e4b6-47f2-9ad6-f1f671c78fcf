package com.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.project.mapper.UserInfoMapper;
import com.project.pojo.entity.UserInfo;
import com.project.service.UserInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

/**
 * 用户信息Service实现类
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Service
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {

    @Override
    public UserInfo getByUsername(String username) {
        LambdaQueryWrapper<UserInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserInfo::getUsername, username);
        return getOne(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addAdmin(UserInfo userInfo) {
        // 检查用户名是否已存在
        if (getByUsername(userInfo.getUsername()) != null) {
            throw new RuntimeException("用户名已存在");
        }

        // 设置默认值
        userInfo.setRole("管理员");
        userInfo.setStatus(true);
        userInfo.setCreatedAt(LocalDateTime.now());
        userInfo.setUpdatedAt(LocalDateTime.now());

        // 密码加密
        String encryptedPassword = DigestUtils.md5DigestAsHex(
                userInfo.getPassword().getBytes(StandardCharsets.UTF_8));
        userInfo.setPassword(encryptedPassword);

        return save(userInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Integer userId, Boolean status) {
        UserInfo userInfo = getById(userId);
        if (userInfo == null) {
            throw new RuntimeException("用户不存在");
        }

        userInfo.setStatus(status);
        userInfo.setUpdatedAt(LocalDateTime.now());

        return updateById(userInfo);
    }

    @Override
    public UserInfo staffLogin(String username, String password) {
        UserInfo user = getByUsername(username);
        if (user == null) {
            throw new RuntimeException("用户名或密码错误");
        }

        // 验证密码（MD5加密）
        String encryptedPassword = DigestUtils.md5DigestAsHex(
                password.getBytes(StandardCharsets.UTF_8));
        if (!encryptedPassword.equals(user.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }

        if (!user.getStatus()) {
            throw new RuntimeException("账户已被禁用，请联系管理员");
        }

        return user;
    }
}