package com.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.project.mapper.UserInfoMapper;
import com.project.pojo.entity.UserInfo;
import com.project.service.UserInfoService;
import org.springframework.stereotype.Service;

/**
 * Created with IntelliJ IDEA.
 *
 * @Project : decoction-center
 * @Package : com.project.service.impl
 * @ClassName :
 * @createTime : 2025/7/1 14:05
 * @version : 1.0
 * <AUTHOR> BING
 * @Description :
 */

@Service
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper , UserInfo> implements UserInfoService{
    @Override
    public UserInfo getByUsername(String username){
        LambdaQueryWrapper<UserInfo> wrapper = new LambdaWueryWrapper<>();
        wrapper.eq(UserInfo::getUsername)
    }
}
