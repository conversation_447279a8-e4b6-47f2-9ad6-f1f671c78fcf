package com.project.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.project.mapper.PatientMapper;
import com.project.pojo.entity.Patient;
import com.project.service.PatientService;
import org.springframework.stereotype.Service;

/**
 * 患者信息Service实现类
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Service
public class PatientServiceImpl extends ServiceImpl<PatientMapper, Patient> implements PatientService {
    
    @Override
    public Patient findByPhoneAndIdCard(String phone, String idCard) {
        return baseMapper.findByPhoneAndIdCard(phone, idCard);
    }
    
    @Override
    public Patient patientLogin(String phone, String idCard) {
        Patient patient = findByPhoneAndIdCard(phone, idCard);
        if (patient == null) {
            throw new RuntimeException("患者信息不存在，请联系管理员");
        }
        if (!patient.getStatus()) {
            throw new RuntimeException("患者账户已被禁用，请联系管理员");
        }
        return patient;
    }
}
