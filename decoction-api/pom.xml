<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.project</groupId>
        <artifactId>decoction-center-system</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>decoction-api</artifactId>
    <packaging>jar</packaging>

    <name>decoction-api</name>
    <description>控制器模块（Controller 层，开放接口）</description>

    <dependencies>
        <!-- 通用模块 -->
        <dependency>
            <groupId>com.project</groupId>
            <artifactId>decoction-common</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- POJO 模块 -->
        <dependency>
            <groupId>com.project</groupId>
            <artifactId>decoction-pojo</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- Service 模块 -->
        <dependency>
            <groupId>com.project</groupId>
            <artifactId>decoction-service</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- 业务应用模块 -->
        <dependency>
            <groupId>com.project</groupId>
            <artifactId>decoction-application-prescription</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.project</groupId>
            <artifactId>decoction-application-dispense</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.project</groupId>
            <artifactId>decoction-application-review</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.project</groupId>
            <artifactId>decoction-application-decoction</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.project</groupId>
            <artifactId>decoction-application-package</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.project</groupId>
            <artifactId>decoction-application-storage</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.project</groupId>
            <artifactId>decoction-application-patient</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
    </dependencies>
</project>
