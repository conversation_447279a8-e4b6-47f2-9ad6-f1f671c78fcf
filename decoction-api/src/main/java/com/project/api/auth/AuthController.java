package com.project.api.auth;

import com.project.common.enums.UserRoleEnum;
import com.project.common.result.Result;
import com.project.pojo.dto.StaffLoginDTO;
import com.project.pojo.dto.PatientLoginDTO;
import com.project.pojo.entity.Patient;
import com.project.pojo.entity.UserInfo;
import com.project.service.PatientService;
import com.project.service.UserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Api(tags = "用户认证")
@RestController
@RequestMapping("/auth")
public class AuthController {
    
    @Autowired
    private UserInfoService userInfoService;
    
    @Autowired
    private PatientService patientService;
    
    /**
     * 员工登录接口
     */
    @ApiOperation("员工登录")
    @PostMapping("/staff/login")
    public Result<Map<String, Object>> staffLogin(@ApiParam("员工登录信息") @Valid @RequestBody StaffLoginDTO loginRequest) {
        try {
            // 临时测试：直接返回成功响应，不查询数据库
            if ("111".equals(loginRequest.getEmployeeId()) && "123456".equals(loginRequest.getPassword())) {
                Map<String, Object> result = new HashMap<>();
                result.put("userType", "STAFF");
                result.put("employeeId", loginRequest.getEmployeeId());
                result.put("role", "ADMIN");
                result.put("roleName", "管理员");
                result.put("message", "登录成功（测试模式）");

                return Result.success(result);
            } else {
                return Result.error("工号或密码错误");
            }

        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 患者登录接口
     */
    @ApiOperation("患者登录")
    @PostMapping("/patient/login")
    public Result<Map<String, Object>> patientLogin(@ApiParam("患者登录信息") @Valid @RequestBody PatientLoginDTO loginRequest) {
        try {
            // 临时测试：直接返回成功响应，不查询数据库
            if ("13800138000".equals(loginRequest.getPhone()) && "110101199001011234".equals(loginRequest.getIdCard())) {
                Map<String, Object> result = new HashMap<>();
                result.put("userType", "PATIENT");
                result.put("phone", loginRequest.getPhone());
                result.put("idCard", loginRequest.getIdCard());
                result.put("role", "PATIENT");
                result.put("roleName", "患者");
                result.put("message", "登录成功（测试模式）");

                return Result.success(result);
            } else {
                return Result.error("手机号或身份证号错误");
            }

        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

}
