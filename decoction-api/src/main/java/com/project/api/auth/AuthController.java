package com.project.api.auth;

import com.project.common.enums.UserRoleEnum;
import com.project.common.result.Result;
import com.project.pojo.dto.LoginRequestDTO;
import com.project.pojo.entity.Patient;
import com.project.pojo.entity.UserInfo;
import com.project.service.PatientService;
import com.project.service.UserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Api(tags = "用户认证")
@RestController
@RequestMapping("/auth")
public class AuthController {
    
    @Autowired
    private UserInfoService userInfoService;
    
    @Autowired
    private PatientService patientService;
    
    /**
     * 统一登录接口
     */
    @ApiOperation("用户登录")
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@ApiParam("登录信息") @Valid @RequestBody LoginRequestDTO loginRequest) {
        try {
            Map<String, Object> result = new HashMap<>();
            
            if ("STAFF".equals(loginRequest.getLoginType())) {
                // 员工登录
                if (!StringUtils.hasText(loginRequest.getUsername()) || !StringUtils.hasText(loginRequest.getPassword())) {
                    return Result.error("用户名和密码不能为空");
                }
                
                UserInfo user = userInfoService.staffLogin(loginRequest.getUsername(), loginRequest.getPassword());
                result.put("userType", "STAFF");
                result.put("userInfo", user);
                result.put("role", user.getRole());
                result.put("roleName", UserRoleEnum.getByCode(user.getRole()).getName());
                
            } else if ("PATIENT".equals(loginRequest.getLoginType())) {
                // 患者登录
                if (!StringUtils.hasText(loginRequest.getPhone()) || !StringUtils.hasText(loginRequest.getIdCard())) {
                    return Result.error("手机号和身份证号不能为空");
                }
                
                Patient patient = patientService.patientLogin(loginRequest.getPhone(), loginRequest.getIdCard());
                result.put("userType", "PATIENT");
                result.put("userInfo", patient);
                result.put("role", "PATIENT");
                result.put("roleName", "患者");
                
            } else {
                return Result.error("不支持的登录类型");
            }
            
            return Result.success(result);
            
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取所有用户角色
     */
    @ApiOperation("获取用户角色列表")
    @GetMapping("/roles")
    public Result<Map<String, String>> getRoles() {
        Map<String, String> roles = new HashMap<>();
        for (UserRoleEnum role : UserRoleEnum.values()) {
            if (!role.isPatient()) { // 排除患者角色，患者不在管理员管理范围内
                roles.put(role.getCode(), role.getName());
            }
        }
        return Result.success(roles);
    }
}
