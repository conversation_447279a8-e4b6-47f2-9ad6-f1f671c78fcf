package com.project.api;

import com.project.common.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试控制器
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */
@Api(tags = "系统测试")
@RestController
@RequestMapping("/test")
public class TestController {
    
    @ApiOperation("系统健康检查")
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("中药煎药中心管理系统运行正常！");
    }

    @ApiOperation("获取系统信息")
    @GetMapping("/info")
    public Result<Object> info() {
        return Result.success("系统版本：1.0.0，构建时间：2025-07-01");
    }

    @ApiOperation("测试登录接口路由")
    @GetMapping("/auth-test")
    public Result<String> authTest() {
        return Result.success("认证模块路由测试成功！");
    }
}
