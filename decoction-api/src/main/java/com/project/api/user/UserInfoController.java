package com.project.api.user;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.project.common.result.Result;
import com.project.common.enums.UserRoleEnum;
import com.project.pojo.dto.UserInfoDTO;
import com.project.pojo.entity.UserInfo;
import com.project.pojo.vo.UserInfoVO;
import com.project.service.UserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息Controller
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/admin/user")
public class UserInfoController {

    @Autowired
    private UserInfoService userInfoService;

    /**
     * 分页查询用户列表
     */
    @ApiOperation("分页查询用户列表")
    @GetMapping("/page")
    public Result<Page<UserInfoVO>> page(
            @ApiParam("页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
            @ApiParam("每页大小") @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @ApiParam("用户姓名") @RequestParam(value = "name", required = false) String name) {

        // 构建查询条件
        LambdaQueryWrapper<UserInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(name != null, UserInfo::getName, name);
        wrapper.orderByDesc(UserInfo::getCreatedAt);

        // 执行查询
        Page<UserInfo> pageInfo = userInfoService.page(new Page<>(page, pageSize), wrapper);

        // 转换结果
        Page<UserInfoVO> voPage = new Page<>(pageInfo.getCurrent(), pageInfo.getSize(), pageInfo.getTotal());
        List<UserInfoVO> voList = pageInfo.getRecords().stream().map(entity -> {
            UserInfoVO vo = new UserInfoVO();
            BeanUtils.copyProperties(entity, vo);
            return vo;
        }).collect(Collectors.toList());
        voPage.setRecords(voList);

        return Result.success(voPage);
    }

    /**
     * 根据ID查询用户
     */
    @ApiOperation("根据ID查询用户详情")
    @GetMapping("/{id}")
    public Result<UserInfoVO> getById(@ApiParam("用户ID") @PathVariable Integer id) {
        UserInfo userInfo = userInfoService.getById(id);
        if (userInfo == null) {
            return Result.error("用户不存在");
        }

        UserInfoVO vo = new UserInfoVO();
        BeanUtils.copyProperties(userInfo, vo);

        return Result.success(vo);
    }

    /**
     * 添加管理员用户
     */
    @ApiOperation("添加管理员用户")
    @PostMapping("/admin")
    public Result<Void> addAdmin(@ApiParam("用户信息") @RequestBody UserInfoDTO userInfoDTO) {
        UserInfo userInfo = new UserInfo();
        BeanUtils.copyProperties(userInfoDTO, userInfo);

        try {
            userInfoService.addAdmin(userInfo);
            return Result.success();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 更新用户状态
     */
    @ApiOperation("更新用户状态")
    @PutMapping("/status")
    public Result<Void> updateStatus(
            @ApiParam("用户ID") @RequestParam("userId") Integer userId,
            @ApiParam("用户状态") @RequestParam("status") Boolean status) {

        try {
            userInfoService.updateStatus(userId, status);
            return Result.success();
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除用户
     */
    @ApiOperation("删除用户")
    @DeleteMapping("/{id}")
    public Result<Void> delete(@ApiParam("用户ID") @PathVariable Integer id) {
        if (userInfoService.removeById(id)) {
            return Result.success();
        } else {
            return Result.error("删除失败");
        }
    }
}