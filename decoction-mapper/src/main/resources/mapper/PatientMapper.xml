<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.mapper.PatientMapper">

    <!-- 根据手机号和身份证号查询患者 -->
    <select id="findByPhoneAndIdCard" resultType="com.project.pojo.entity.Patient">
        SELECT * FROM patient 
        WHERE phone = #{phone} AND id_card = #{idCard} AND status = true
    </select>

</mapper>
