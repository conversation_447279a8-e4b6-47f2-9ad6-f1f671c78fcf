package com.project.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.project.pojo.entity.Patient;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 患者信息Mapper
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Mapper
public interface PatientMapper extends BaseMapper<Patient> {
    
    /**
     * 根据手机号和身份证号查询患者
     */
    Patient findByPhoneAndIdCard(@Param("phone") String phone, @Param("idCard") String idCard);
}
