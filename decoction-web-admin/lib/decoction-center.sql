create table hospital
(
    hospital_id varchar(50)  not null
        primary key,
    name        varchar(200) not null,
    grade       varchar(50),
    type        varchar(50),
    region      varchar(100),
    phone       varchar(20),
    email       varchar(100),
    address     varchar(300) not null
)
    with (orientation = row, compression = no);

alter table hospital
    owner to user02;

create table doctor
(
    doctor_id   varchar(50)  not null
        primary key,
    name        varchar(100) not null,
    gender      varchar(1)   not null
        constraint doctor_gender_check
            check ((gender)::text = ANY ((ARRAY ['M'::character varying, 'F'::character varying])::text[])),
    title       varchar(50),
    department  varchar(100),
    hospital_id varchar(50)  not null,
    phone       varchar(20),
    email       varchar(100),
    license_no  varchar(50),
    status      varchar(20)  not null
        constraint doctor_status_check
            check ((status)::text = ANY ((ARRAY ['active'::character varying, 'inactive'::character varying])::text[])),
    notes       text
)
    with (orientation = row, compression = no);

alter table doctor
    owner to user02;

create table patient
(
    patient_id      varchar(50)  not null
        primary key,
    name            varchar(100) not null,
    gender          varchar(1)   not null
        constraint patient_gender_check
            check ((gender)::text = ANY ((ARRAY ['M'::character varying, 'F'::character varying])::text[])),
    id_card         varchar(18),
    age             integer      not null,
    phone_mobile    varchar(20),
    nationality     varchar(50),
    marital_status  varchar(20),
    addr_permanent  varchar(300),
    addr_current    varchar(300) not null,
    postal_code     varchar(10),
    allergy_history text,
    past_history    text
)
    with (orientation = row, compression = no);

alter table patient
    owner to user02;

create table cabinet
(
    cabinet_id            varchar(50)  not null
        primary key,
    box_id                varchar(50),
    name                  varchar(100) not null,
    location              varchar(200),
    slots_total           integer      not null,
    slots_used            integer      not null,
    status                varchar(20)  not null
        constraint cabinet_status_check
            check ((status)::text = ANY ((ARRAY ['active'::character varying, 'inactive'::character varying])::text[])),
    install_date          date,
    last_maintenance_date date,
    notes                 text
)
    with (orientation = row, compression = no);

alter table cabinet
    owner to user02;

create table prescription
(
    prescription_id varchar(50) not null
        primary key,
    hospital_id     varchar(50) not null,
    patient_id      varchar(50) not null,
    department      varchar(100),
    doctor_id       varchar(50) not null,
    presc_date      date        not null,
    diagnosis       text        not null,
    chief_complaint text        not null,
    past_history    text,
    allergy_history text,
    medical_orders  text        not null
)
    with (orientation = row, compression = no);

alter table prescription
    owner to user02;

create table herb_location
(
    location_code    varchar(50) not null
        primary key,
    cabinet_id       varchar(50) not null,
    slot_number      integer     not null,
    capacity         numeric(10, 2),
    current_quantity numeric(10, 2),
    status           varchar(20) not null
        constraint herb_location_status_check
            check ((status)::text = ANY ((ARRAY ['active'::character varying, 'inactive'::character varying])::text[])),
    notes            text
)
    with (orientation = row, compression = no);

alter table herb_location
    owner to user02;

create table herb_storage
(
    batch_no      varchar(50)    not null
        primary key,
    herb_id       varchar(50)    not null,
    herb_name     varchar(200)   not null,
    specification varchar(100)   not null,
    unit          varchar(20)    not null,
    quantity      numeric(10, 2) not null,
    expiry_date   date,
    location_code varchar(50)    not null,
    supplier      varchar(200),
    notes         text
)
    with (orientation = row, compression = no);

alter table herb_storage
    owner to user02;

create table inbound
(
    inbound_id  varchar(50)    not null
        primary key,
    batch_no    varchar(50)    not null,
    herb_id     varchar(50)    not null,
    quantity    numeric(10, 2) not null,
    unit        varchar(20)    not null,
    timestamp   timestamp      not null,
    document_no varchar(100)   not null,
    source_type varchar(20),
    notes       text
)
    with (orientation = row, compression = no);

alter table inbound
    owner to user02;

create table medicine_slot
(
    slot_id          varchar(50)    not null
        primary key,
    cabinet_id       varchar(50)    not null,
    herb_id          varchar(50)    not null,
    quantity         numeric(10, 2) not null,
    last_refill_time timestamp,
    status           varchar(20)    not null
        constraint medicine_slot_status_check
            check ((status)::text = ANY ((ARRAY ['active'::character varying, 'inactive'::character varying])::text[])),
    notes            text
)
    with (orientation = row, compression = no);

alter table medicine_slot
    owner to user02;

create table presc_medicine
(
    item_id         varchar(50)  not null
        primary key,
    prescription_id varchar(50)  not null,
    herb_id         varchar(50)  not null,
    herb_name       varchar(200) not null,
    specification   varchar(100) not null,
    dose_each       varchar(50)  not null,
    dose_unit       varchar(20)  not null,
    times_per_day   integer,
    days            integer,
    total_dose      varchar(50),
    usage_method    varchar(100) not null,
    usage_dosage    varchar(200)
)
    with (orientation = row, compression = no);

alter table presc_medicine
    owner to user02;

create table decoction
(
    prescription_id          varchar(50)  not null
        primary key,
    soak_time                integer,
    rounds                   integer default 1,
    pre_decoction_duration   integer,
    main_decoction1_duration integer,
    post_decoction1_time     integer,
    main_decoction2_duration integer,
    post_decoction2_time     integer,
    special_methods          varchar(100) not null,
    temperature_profile      json,
    notes                    text
)
    with (orientation = row, compression = no);

alter table decoction
    owner to user02;

create table delivery
(
    prescription_id  varchar(50)  not null
        primary key,
    recipient_name   varchar(100) not null,
    recipient_phone  varchar(20)  not null,
    is_self_pickup   boolean      not null,
    cabinet_id       varchar(50),
    delivery_address varchar(300) not null,
    delivery_notes   text         not null
)
    with (orientation = row, compression = no);

alter table delivery
    owner to user02;

create table prescription_process_summary
(
    prescription_id       varchar(50) not null
        primary key,
    basket_id             varchar(50) not null,
    received_by           varchar(50) not null,
    received_time         timestamp   not null,
    approved_by           varchar(50) not null,
    approved_time         timestamp   not null,
    dispensed_by          varchar(50) not null,
    dispensed_time        timestamp   not null,
    reviewed_by           varchar(50) not null,
    reviewed_time         timestamp   not null,
    re_dispensed_by       varchar(50),
    re_dispensed_time     timestamp,
    decoction_started_by  varchar(50) not null,
    decoction_start_time  timestamp   not null,
    first_decoction_by    varchar(50) not null,
    first_decoction_time  timestamp   not null,
    second_decoction_by   varchar(50),
    second_decoction_time timestamp,
    packaged_by           varchar(50) not null,
    packaged_time         timestamp   not null,
    pickup_or_ship_by     varchar(50) not null,
    pickup_or_ship_time   timestamp   not null,
    status_overall        varchar(20) not null
        constraint prescription_process_summary_status_overall_check
            check ((status_overall)::text = ANY
                   ((ARRAY ['pending'::character varying, 'in_progress'::character varying, 'completed'::character varying, 'issue'::character varying])::text[])),
    notes                 text
)
    with (orientation = row, compression = no);

alter table prescription_process_summary
    owner to user02;

create table dispense_proof
(
    proof_id         varchar(50) not null
        primary key,
    prescription_id  varchar(50) not null,
    phase            varchar(20) not null
        constraint dispense_proof_phase_check
            check ((phase)::text = ANY ((ARRAY ['dispense'::character varying, 'review'::character varying])::text[])),
    image_urls       text        not null,
    weighing_details text        not null,
    approved         boolean     not null,
    timestamp        timestamp   not null,
    notes            text        not null
)
    with (orientation = row, compression = no);

alter table dispense_proof
    owner to user02;

create table packaging_proof
(
    proof_id          varchar(50)    not null
        primary key,
    prescription_id   varchar(50)    not null,
    scenario          varchar(20)    not null
        constraint packaging_proof_scenario_check
            check ((scenario)::text = ANY
                   ((ARRAY ['dispense'::character varying, 'decoction'::character varying])::text[])),
    image_urls        text           not null,
    weighing_total    numeric(10, 2) not null,
    package_integrity varchar(20)    not null
        constraint packaging_proof_package_integrity_check
            check ((package_integrity)::text = ANY
                   ((ARRAY ['qualified'::character varying, 'unqualified'::character varying])::text[])),
    liquid_inspection varchar(20)    not null
        constraint packaging_proof_liquid_inspection_check
            check ((liquid_inspection)::text = ANY
                   ((ARRAY ['qualified'::character varying, 'unqualified'::character varying])::text[])),
    microbial_limit   varchar(100)   not null,
    approved          boolean        not null,
    timestamp         timestamp      not null,
    notes             text           not null
)
    with (orientation = row, compression = no);

alter table packaging_proof
    owner to user02;

create table user_info
(
    user_id    serial
        primary key,
    name       varchar(100) not null,
    gender     varchar(10)  not null
        constraint user_info_gender_check
            check ((gender)::text = ANY ((ARRAY ['男'::character varying, '女'::character varying])::text[])),
    age        integer
        constraint user_info_age_check
            check ((age >= 18) AND (age <= 100)),
    role       varchar(50)  not null,
    username   varchar(100) not null
        unique,
    password   varchar(200) not null,
    sign_data  text,
    status     boolean   default true,
    created_at timestamp default pg_systimestamp(),
    updated_at timestamp default pg_systimestamp()
)
    with (orientation = row, compression = no);

alter table user_info
    owner to fb;

create table decoction_device
(
    device_id               varchar(50)                                   not null
        primary key,
    name                    varchar(100)                                  not null,
    model                   varchar(50),
    manufacturer            varchar(100),
    purchase_date           date,
    install_date            date,
    location                varchar(200),
    status                  varchar(20) default '空闲'::character varying not null
        constraint decoction_device_status_check
            check ((status)::text = ANY
                   ((ARRAY ['空闲'::character varying, '使用中'::character varying, '维护中'::character varying, '停用'::character varying])::text[])),
    support_mode            text,
    current_prescription_id varchar(50),
    last_maintenance_time   timestamp,
    next_maintenance_due    date,
    remarks                 text
)
    with (orientation = row, compression = no);

alter table decoction_device
    owner to fb;


