package com.project.common.enums;

/**
 * 用户角色枚举
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public enum UserRoleEnum {
    
    /**
     * 系统管理员
     */
    ADMIN("ADMIN", "系统管理员"),
    
    /**
     * 仓储管理员
     */
    STORAGE_MANAGER("STORAGE_MANAGER", "仓储管理员"),
    
    /**
     * 仓储员
     */
    STORAGE_STAFF("STORAGE_STAFF", "仓储员"),
    
    /**
     * 接单审方员
     */
    PRESCRIPTION_REVIEWER("PRESCRIPTION_REVIEWER", "接单审方员"),
    
    /**
     * 调剂员
     */
    DISPENSER("DISPENSER", "调剂员"),
    
    /**
     * 复核员
     */
    CHECKER("CHECKER", "复核员"),
    
    /**
     * 煎药员
     */
    DECOCTION_STAFF("DECOCTION_STAFF", "煎药员"),
    
    /**
     * 包装质检员
     */
    PACKAGE_QC("PACKAGE_QC", "包装质检员"),
    
    /**
     * 患者
     */
    PATIENT("PATIENT", "患者");
    
    private final String code;
    private final String name;
    
    UserRoleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据代码获取角色
     */
    public static UserRoleEnum getByCode(String code) {
        for (UserRoleEnum role : values()) {
            if (role.getCode().equals(code)) {
                return role;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为管理员角色
     */
    public boolean isAdmin() {
        return this == ADMIN;
    }
    
    /**
     * 判断是否为患者角色
     */
    public boolean isPatient() {
        return this == PATIENT;
    }
    
    /**
     * 判断是否为工作人员角色
     */
    public boolean isStaff() {
        return !isPatient();
    }
}
